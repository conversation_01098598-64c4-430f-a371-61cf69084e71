[package]
name = "JustCooked"
version = "0.1.0"
description = "A modern digital cookbook app"
authors = ["<PERSON>"]
edition = "2021"
default-run = "JustCooked"

[lints.rust]
non_snake_case = "allow"

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2.0", features = [] }
tauri-plugin-fs = "2.0"
tauri-plugin-http = "2.0"
tauri-plugin-shell = "2.0"
tauri-plugin-dialog = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.12", features = ["json"] }
tokio = { version = "1", features = ["full"] }
scraper = "0.20"
url = "2.5"
regex = "1.10"
image = "0.25"
uuid = { version = "1.0", features = ["v4"] }
tokio-util = "0.7"
base64 = "0.22"
chrono = { version = "0.4", features = ["serde"] }
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.8"
mockall = "0.12"
wiremock = "0.6"
proptest = "1.4"

[lib]
name = "JustCooked"
crate-type = ["cdylib", "rlib"]

[features]
default = [ ]
